// This file is auto-generated, don't edit it. Thanks.
package com.aliyun.sample;

import com.aliyun.tea.*;

public class Sample {

    /**
     * <b>description</b> :
     * <p>使用凭据初始化账号Client</p>
     * @return Client
     * 
     * @throws Exception
     */
    public static com.aliyun.fc20230330.Client createClient() throws Exception {
        // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.credentials.Client credential = new com.aliyun.credentials.Client();
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setCredential(credential);
        // Endpoint 请参考 https://api.aliyun.com/product/FC
        config.endpoint = "" + "ALICLOUD_ACCOUNT_ID" + ".cn-hangzhou.fc.aliyuncs.com";
        return new com.aliyun.fc20230330.Client(config);
    }

    public static void main(String[] args_) throws Exception {
        
        com.aliyun.fc20230330.Client client = Sample.createClient();
        // 需要安装额外的依赖库，直接点击下载完整工程即可看到所有依赖。
        java.io.InputStream bodyStream = com.aliyun.darabonba.stream.Client.readFromString("{\"key\":\"value\"}");
        com.aliyun.fc20230330.models.InvokeFunctionHeaders invokeFunctionHeaders = new com.aliyun.fc20230330.models.InvokeFunctionHeaders()
                .setXFcInvocationType("Sync")
                .setXFcLogType("None");
        com.aliyun.fc20230330.models.InvokeFunctionRequest invokeFunctionRequest = new com.aliyun.fc20230330.models.InvokeFunctionRequest()
                .setQualifier("LATEST")
                .setBody(bodyStream);
        
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            com.aliyun.fc20230330.models.InvokeFunctionResponse resp = client.invokeFunctionWithOptions("lx-tuomin-dev", invokeFunctionRequest, invokeFunctionHeaders, runtime);
            com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(resp));
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }        
    }
}
